# 数据源说明表

## 数据源详细说明

### 1. LU数据 (Learning Unit - 学习单元数据)
**访问方式**: `GetLuData` 方法，通过 dataproxy 服务获取
**数据类型**:
- 预习相关: previewCorrectNum, previewParticipateNum, previewTotalNum, isPreviewFinish
- 到课相关: attendDuration, isAttendFinish
- 回放相关: playbackTotalTime, playbackTimeAfterUnlock, inclassTeacherRoomTotalPlaybackTimeV1
- 录播相关: lbpAttendDuration
- 口述题相关: oralQuestionSubmit, oralQuestionCorrectTime
- 堂堂测相关: tangTangExamCorrectNum, tangTangExamParticipateNum, isTangTangExamSubmit
- 作业相关: homeworkLevel
- 互动题相关: mixLiveInteractionRightNum, mixLiveInteractionSubmitNum, mixPlaybackInteractionRightNum, mixPlaybackInteractionSubmitNum
- 小鹿相关: deerProgrammingHomeworkLevel, deerProgrammingHomeworkStatus
- 同步练习: synchronousPracticeCorrectNum, synchronousPracticeParticipateNum
- 聊天相关: chatNum
- 相似题相关: exam_answer.exam33.*

### 2. CommonLU数据 (公共学习单元数据)
**访问方式**: `GetCommonLuData` 方法，通过 dataproxy 服务获取
**数据类型**:
- 互动题相关: inclassRightCnt, inclassParticipateCnt, inclassQuestionCnt, playbackRightCnt, playbackParticipateCnt
- 录播相关: inclassTeacherRoomTotalPlaybackContentTime, inclassTeacherToomTotalPlaybackContentTime
- 到课相关: isInclassTeacherRoomAttend30minute, isInclassTeacherRoomTotalPlaybackContentTimeGe3min, isInclassTeacherRoomTotalPlaybackContentTimeEqLessonVideoDuration
- 作品相关: isSubmitLessonWork
- 报告相关: isGenerateLessonReport

### 3. DAL CourseInfo (课程信息)
**访问方式**: `GetCourseInfo` 方法，通过 DAL 服务获取
**数据类型**:
- 课程基础信息: courseId, mainGradeId, mainSubjectId
- 章节列表: lessonList (包含章节名称、类型、播放类型等)
- 课程类型判断: 用于判断是否为LPC课程等

### 4. DAS数据 (Data Analysis Service - 数据分析服务)
**访问方式**: `GetDasLessonsData` 方法
**数据类型**:
- 作业状态: homeworkStatus, homeworkRecorrect
- 到课状态: isAttended
- 学生章节信息: StudentLessonInfo

### 5. 考试绑定数据
**访问方式**: `GetExamRelationData` 方法
**数据类型**:
- 绑定状态: bindStatus
- 题目总数: totalNum
- 人工批改标识: isArtificialCorrect
- 支持的绑定类型: 口述题、堂堂测、课中练习、同步练习等

### 6. LPC学生数据 (Live Private Class - 直播私教课)
**访问方式**: `GetLpcStudentData` 方法
**数据类型**:
- 到课相关: attend, isLbpAttend, isAiAttend
- 完课相关: isLbpAttendFinish, isAiFinish, isAttendFinish
- 回放相关: playbackTime
- 考试相关: exam1, exam5, exam10, exam13 (包含rightNum, participateNum, totalNum, isSubmit等)
- 互动相关: inClassHandsUpNum, inClassVideoLinkNum

### 7. 章节信息
**访问方式**: `GetLessonInfoMap`, `GetLessonBaseInfo` 等方法
**数据类型**:
- 基础信息: lessonId, startTime, stopTime, playType
- 特殊标识: t007Tag
- 状态信息: lessonStatus

### 8. ILab信息 (实验室数据)
**访问方式**: `GetILabInfo` 方法
**数据类型**:
- 章节检查: CheckIlabLesson
- 预习信息: PreviewInfoByIlab
- 堂堂测信息: InclassTestInfoByIlab
- 作业信息: HomeworkInfoByIlab, HomeworkInfoByNomal
- 版本信息: Version (v1/v2)

### 9. 作业相关数据
**访问方式**: 
- `GetHomeworkBindExams` - 作业绑定状态
- `GetHomeworkOpenInfo` - 作业开启信息
**数据类型**:
- 绑定状态: 章节是否绑定作业
- 开启状态: 课程作业是否开启

### 10. 学分数据
**访问方式**: `GetStudentLessonsScore` 方法，调用 jxdascore 接口
**数据类型**:
- 学分信息: score

### 11. 小鹿数据
**访问方式**: `GetDeerData` 方法
**数据类型**:
- 口才作业: deerEloquenceHomeworkLevel
- 编程作业: deerProgrammingHomeworkLevel

### 12. 外部服务
**Moat短链接服务**: 用于生成短链接
**WriteReport API**: 用于获取报告URL模板
**PcAssistant API**: 用于获取作文配置、审核信息等

### 13. 数据库直接查询
**学生章节请假信息**: tblLessonStudent 表，包含请假状态和原因
**教师映射**: DAT teacherlesson + DAU teacher 表关联

### 14. 配置数据
**月考报告配置**: `GetMonthlyExamReportLessons`
**作文配置**: `GetCompositionConf`
**学习计划数据**: `GetLearningPlansData`
**预习开启状态**: `GetPreviewOpenInfo`
**审核映射**: `GetLessonNeedAuditMap`
**课堂报告数据**: `GetLessonReportData`
**巩固练习状态**: `GetLessonStrengthPracticeStatus`
**章节数据**: `GetLessonDataByLessonIds`
**教师映射**: `GetLessonTeacherMap`

## 数据源使用统计

### 最常用的数据源 (按使用频次排序)
1. **LU数据** - 被23个方法使用，是最核心的学习数据源
2. **课程信息(DAL CourseInfo)** - 被15个方法使用，提供基础课程和章节信息
3. **CommonLU数据** - 被12个方法使用，主要用于互动题和观看时长统计
4. **章节信息** - 被8个方法使用，提供章节基础属性
5. **LPC学生数据** - 被8个方法使用，专门用于LPC课程相关功能
6. **考试绑定数据** - 被7个方法使用，用于各类练习和考试功能
7. **DAS数据** - 被4个方法使用，主要用于到课状态和作业状态

### 复杂度最高的方法
1. **GetHomeworkData** - 使用8个不同数据源
2. **GetInclassTest** - 使用4个数据源
3. **GetPreview** - 使用4个数据源

