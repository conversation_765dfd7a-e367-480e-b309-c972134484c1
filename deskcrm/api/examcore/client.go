package examcore

import (
	"deskcrm/api"
	"deskcrm/api/dal"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"deskcrm/util"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Client examcore API客户端
type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.ExamCore,
	}
	return c
}

const (
	getAnswerApi   = "/examcore/v1/getanswer"   // 获取作答信息
	getRelationApi = "/examcore/v1/getrelation" // 获取绑定关系
	getExamApi     = "/examcore/v1/getexam"     // 获取试卷信息

	// 认证常量，对应PHP中的APP_ID和APP_SECRET
	appID     = 10000010
	appSecret = "22f0da0c"
)

func (c *Client) GetRelation(ctx *gin.Context, bindList []string) (map[string]map[int64]BindInfo, error) {
	// 1. 兼容试卷绑定到大纲上
	enhancedBindList, outlineLessonMap, err := c.GetOutlineBindList(ctx, bindList)
	if err != nil {
		zlog.Errorf(ctx, "GetRelation getOutlineBindList failed: %v", err)
		// 如果获取outline绑定失败，继续使用原始bindList
		enhancedBindList = bindList
		outlineLessonMap = make(map[int]*dal.LessonInfo)
	}

	// 2. 调用examcore客户端获取绑定关系
	examRelationList, err := c.getRelationApi(ctx, enhancedBindList)
	if err != nil {
		return nil, err
	}

	// 3. 处理大纲兼容的返回结果
	result := c.processOutlineRelation(examRelationList, outlineLessonMap)

	return result, nil
}

// GetOutlineBindList bindList兼容outline绑定新方案
func (c *Client) GetOutlineBindList(ctx *gin.Context, bindList []string) ([]string, map[int]*dal.LessonInfo, error) {
	// 1. 提取lesson相关的绑定，同时记录bindType信息
	var lessonIds []int
	lessonBindMap := make(map[int]string) // lessonId -> bindType的映射
	for _, bindKey := range bindList {
		if strings.Contains(bindKey, "lesson") {
			// 解析格式: lesson_123:456
			parts := strings.Split(bindKey, ":")
			if len(parts) == 2 {
				lessonPart := strings.Split(parts[0], "_")
				if len(lessonPart) == 2 {
					lessonIds = append(lessonIds, cast.ToInt(lessonPart[1]))
					lessonBindMap[cast.ToInt(lessonPart[1])] = parts[1]
				}
			}
		}
	}

	// 2. 如果没有lesson绑定，直接返回原始bindList
	if len(lessonIds) == 0 {
		return bindList, make(map[int]*dal.LessonInfo), nil
	}

	// 3. 获取lesson信息，包含outlineId
	lessonInfoMap, err := dal.GetLessonBaseByLessonIds(ctx, lessonIds, []string{"lessonId", "outlineId"})
	if err != nil {
		zlog.Warnf(ctx, "获取lesson信息失败: %v", err)
		return bindList, make(map[int]*dal.LessonInfo), err
	}

	// 4. 构建lessonInfo映射，用于后续处理
	lessonInfoByIdMap := make(map[int]*dal.LessonInfo)
	for lessonId, lessonInfo := range lessonInfoMap {
		lessonInfoByIdMap[lessonId] = lessonInfo
	}

	// 5. 为每个lesson绑定添加对应的outline绑定
	for _, lessonId := range lessonIds {
		if lessonInfo, exists := lessonInfoByIdMap[lessonId]; exists {
			bindType := lessonBindMap[lessonId]
			outlineId := lessonInfo.OutlineId
			outlineBindKey := fmt.Sprintf("outline_%d:%s", outlineId, bindType)
			bindList = append(bindList, outlineBindKey)
		}
	}

	return bindList, lessonInfoByIdMap, nil
}

// processOutlineRelation 处理outline绑定兼容逻辑
func (c *Client) processOutlineRelation(examRelationList map[string]map[int64]BindInfo, outlineLessonMap map[int]*dal.LessonInfo) map[string]map[int64]BindInfo {
	if len(examRelationList) == 0 || len(outlineLessonMap) == 0 {
		return examRelationList
	}

	// 遍历examRelationList，查找outline绑定并转换为lesson绑定
	for bindKey, relation := range examRelationList {
		// 检查是否为outline绑定（格式：outline_123:456）
		if strings.Contains(bindKey, "outline") {
			// 提取outlineId：从"outline_123:456"中提取"123"
			parts := strings.Split(bindKey, ":")
			if len(parts) != 2 {
				continue
			}

			bindPart := parts[0] // "outline_123"
			bindType := parts[1] // "456"

			outlineIdParts := strings.Split(bindPart, "_")
			if len(outlineIdParts) != 2 || outlineIdParts[0] != "outline" {
				continue
			}

			outlineIdStr := outlineIdParts[1] // "123"
			outlineId, err := strconv.Atoi(outlineIdStr)
			if err != nil {
				zlog.Warnf(nil, "processOutlineRelation: 解析outlineId失败: %s, err: %v", outlineIdStr, err)
				continue
			}

			// 在outlineLessonMap中查找对应的lesson
			for _, lessonInfo := range outlineLessonMap {
				if lessonInfo.OutlineId == outlineId {
					// 构建lesson绑定key：lesson_456:789
					lessonKey := fmt.Sprintf("lesson_%d:%s", lessonInfo.LessonId, bindType)

					// 如果lesson绑定不存在，则添加（避免覆盖已有的lesson绑定）
					if _, exists := examRelationList[lessonKey]; !exists || len(examRelationList[lessonKey]) == 0 {
						examRelationList[lessonKey] = relation
						zlog.Debugf(nil, "processOutlineRelation: 添加lesson绑定 %s -> %s", bindKey, lessonKey)
					}
				}
			}
		}
	}
	return examRelationList
}

// GetRelation 获取绑定关系 (分批调用，每批最多10个bindStr)
func (c *Client) getRelationApi(ctx *gin.Context, bindList []string) (map[string]map[int64]BindInfo, error) {
	if len(bindList) == 0 {
		return make(map[string]map[int64]BindInfo), nil
	}

	// 分批处理，每批最多10个bindStr
	batchSize := 10
	result := make(map[string]map[int64]BindInfo)

	for i := 0; i < len(bindList); i += batchSize {
		end := min(i+batchSize, len(bindList))
		batchBindStrs := bindList[i:end]

		// 1. 构建请求参数
		req := map[string]interface{}{
			"appId":     appID,
			"appSecret": appSecret,
			"bindStrs":  batchBindStrs, // 使用当前批次的bindStr
		}

		// 2. 设置请求选项
		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
		utils.DecorateHttpOptions(ctx, &opts)

		// 3. 发送请求
		res, err := c.cli.HttpPost(ctx, getRelationApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetRelation request err (batch %d-%d): %v", i, end-1, err)
			return nil, err // 如果某一批次失败，则整个调用失败
		}

		// 4. 检查 HTTP 状态码
		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		// 5. 解析响应
		var examcoreResp ExamcoreResponse
		if _, err = api.DecodeResponse(ctx, res, &examcoreResp); err != nil {
			return nil, err
		}

		// 解析data.list为目标格式
		var batchResp map[string]map[int64]BindInfo
		listBytes, err := json.Marshal(examcoreResp.List)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetRelation marshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		if err = json.Unmarshal(listBytes, &batchResp); err != nil {
			zlog.Warnf(ctx, "ExamCore GetRelation unmarshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		// 将当前批次的结果合并到总结果中
		for k, v := range batchResp {
			result[k] = v
		}
	}

	return result, nil
}

// GetAnswer 获取学生试卷作答信息 (分批调用，每批最多50个answerKey)
func (c *Client) GetAnswer(ctx *gin.Context, answerKeyList []string) (map[string][]AnswerInfo, error) {
	if len(answerKeyList) == 0 {
		return make(map[string][]AnswerInfo), nil
	}

	// 分批处理，每批最多50个answerKey
	batchSize := 50
	result := make(map[string][]AnswerInfo)

	for i := 0; i < len(answerKeyList); i += batchSize {
		end := min(i+batchSize, len(answerKeyList))
		batchAnswerKeys := answerKeyList[i:end]

		// 1. 构建请求参数
		req := map[string]interface{}{
			"appId":      appID,
			"appSecret":  appSecret,
			"answerKeys": batchAnswerKeys, // 使用当前批次的answerKey
		}

		// 2. 设置请求选项
		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
		utils.DecorateHttpOptions(ctx, &opts)

		// 3. 发送请求
		res, err := c.cli.HttpPost(ctx, getAnswerApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetAnswer request err (batch %d-%d): %v", i, end-1, err)
			return nil, err // 如果某一批次失败，则整个调用失败
		}

		// 4. 检查 HTTP 状态码
		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		// 5. 解析响应
		var examcoreResp ExamcoreResponse
		if _, err = api.DecodeResponse(ctx, res, &examcoreResp); err != nil {
			return nil, err
		}

		// 解析data.list为目标格式
		var batchResp map[string][]AnswerInfo
		listBytes, err := json.Marshal(examcoreResp.List)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetAnswer marshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		if err = json.Unmarshal(listBytes, &batchResp); err != nil {
			zlog.Warnf(ctx, "ExamCore GetAnswer unmarshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		// 将当前批次的结果合并到总结果中
		for k, v := range batchResp {
			result[k] = v
		}
	}

	return result, nil
}

// GetBottomTestSubjectList 获取摸底测学科列表
func (c *Client) GetBottomTestSubjectList(ctx *gin.Context, cpuId int64) (map[int64]int, error) {
	if cpuId == 0 {
		return make(map[int64]int), nil
	}

	// 1. 先获取绑定关系
	bindStr := FormatBindStr(cpuId, BindTypeCpu, ExamTypeSurvey)
	relations, err := c.GetRelation(ctx, []string{bindStr})
	if err != nil {
		return nil, err
	}

	bindRelations, exists := relations[bindStr]
	if !exists || len(bindRelations) == 0 {
		return make(map[int64]int), nil
	}

	// 2. 提取examId列表
	var examIds []int64
	for examId := range bindRelations {
		examIds = append(examIds, examId)
	}

	// 3. 获取试卷信息
	examList, err := c.GetExamInfo(ctx, examIds)
	if err != nil {
		return nil, err
	}

	// 4. 提取学科信息
	result := make(map[int64]int)
	for examId, examInfo := range examList {
		result[examId] = examInfo.Exam.Subject
	}

	return result, nil
}

// GetExamInfo 获取试卷信息
func (c *Client) GetExamInfo(ctx *gin.Context, examIds []int64) (map[int64]ExamDetailInfo, error) {
	if len(examIds) == 0 {
		return make(map[int64]ExamDetailInfo), nil
	}

	// 1. 构建请求参数
	req := map[string]interface{}{
		"appId":           appID,
		"appSecret":       appSecret,
		"examIds":         util.ConvertArrayIntToArrayString(examIds),
		"hasQuestionlist": 0, // 修复：使用正确的参数名称，与PHP版本保持一致
	}

	// 2. 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 3. 发送请求
	res, err := c.cli.HttpPost(ctx, getExamApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "ExamCore GetExamInfo request err: %v", err)
		return nil, err
	}

	// 4. 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 5. 解析响应
	var examcoreResp ExamcoreResponse
	if _, err = api.DecodeResponse(ctx, res, &examcoreResp); err != nil {
		return nil, err
	}

	// 解析data.list为目标格式
	var result map[int64]ExamDetailInfo
	listBytes, err := json.Marshal(examcoreResp.List)
	if err != nil {
		zlog.Warnf(ctx, "ExamCore GetExamInfo marshal list err: %v", err)
		return nil, err
	}

	if err = json.Unmarshal(listBytes, &result); err != nil {
		zlog.Warnf(ctx, "ExamCore GetExamInfo unmarshal list err: %v", err)
		return nil, err
	}

	return result, nil
}
