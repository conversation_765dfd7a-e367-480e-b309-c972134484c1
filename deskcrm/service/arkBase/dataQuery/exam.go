package dataQuery

import (
	"deskcrm/api/assistantdesk"
	"deskcrm/api/dal"
	"deskcrm/api/dataproxy"
	"deskcrm/api/examcore"
	"deskcrm/api/frontcourse"
	"deskcrm/api/jxexamui"
	"deskcrm/api/pcassistant"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/models"
	"fmt"
	"sort"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func (s *Singleton) GetInteractTotalNum(ctx *gin.Context, lessonIds []int64) (map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {

	detailRsp, err := dataproxy.NewClient().GetListByBindIdsBindTypeRelationTypesExamTags(ctx, dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindType:      RelationTypeLesson,
		BindIds:       lessonIds,
		RelationTypes: getTotalNumExamTypes,
		ExamTags:      []int64{0},
		Fields:        []string{"bindId", "relationType", "totalNum", "bindStatus", "is_artificial_correct"},
	})
	if err != nil {
		return nil, err
	}

	resMap := make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range detailRsp {
		resMap[item.BindId] = item
	}

	return resMap, nil
}

func (s *Singleton) GetExamBindData(ctx *gin.Context, lessonId int64, bindType int) (assistantdesk.ExamBindRes, error) {

	detailRsp, err := assistantdesk.NewClient().GetExamBindStatus(ctx, assistantdesk.GetExamBindStatusParams{
		LessonId: lessonId,
		BindType: bindType,
	})
	if err != nil {
		return detailRsp, err
	}

	return detailRsp, nil
}

// GetILabInfo 获取iLab课程信息
// 对应PHP中的initILabInfo方法
func (s *Singleton) GetILabInfo(ctx *gin.Context, lessonIds []int64, studentUid int64) (*ILabInfo, error) {
	result := &ILabInfo{
		PreviewInfoByIlab:     make(map[int64]int),
		HomeworkInfoByIlab:    make(map[int64]int),
		InclassTestInfoByIlab: make(map[int64]int),
		HomeworkInfoByNomal:   make(map[int64]int),
		CheckIlabLesson:       make(map[int64]jxexamui.ExamTypeInfo),
	}

	// 获取章节试卷类型信息
	checkIlabLesson, err := jxexamui.NewClient().GetExamTypeByLessonIds(ctx, lessonIds)
	if err != nil {
		zlog.Errorf(ctx, "GetILabInfo GetExamTypeByLessonIds failed: %v", err)
		return result, nil
	}
	result.CheckIlabLesson = checkIlabLesson

	if len(checkIlabLesson) == 0 {
		return result, nil
	}

	// 获取学生各类型试卷的结果等级
	examTypes := []int{
		jxexamui.BindTypeHomeworkIlab, // iLab巩固练习
		jxexamui.BindTypePosttestMore, // 初高中预习测试
		jxexamui.BindTypeTestInClass,  // 堂堂测
		jxexamui.BindTypeHomework,     // 普通巩固练习
	}

	ilabRet, err := jxexamui.NewClient().GetLevelByExamTypeUidLessonIds(ctx, studentUid, examTypes, lessonIds)
	if err != nil {
		return nil, err
	}

	// 填充结果
	if levels, ok := ilabRet[jxexamui.BindTypeHomeworkIlab]; ok {
		result.HomeworkInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypePosttestMore]; ok {
		result.PreviewInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypeTestInClass]; ok {
		result.InclassTestInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypeHomework]; ok {
		result.HomeworkInfoByNomal = levels
	}

	return result, nil
}

// GetPreviewOpenInfo 获取预习开启状态信息
// 对应PHP中的initPreviewIsOpenInfos方法
func (s *Singleton) GetPreviewOpenInfo(ctx *gin.Context, courseId int64, lessonIds []int64, gradeStage int) (map[int64]PreviewOpenInfo, error) {
	result := make(map[int64]PreviewOpenInfo)

	if courseId == 0 || len(lessonIds) == 0 || gradeStage == 0 {
		return result, nil
	}

	now := time.Now().Unix()

	switch gradeStage {
	case define.GradeStagePrimary, GradeStagePreschool: // 小学和低幼学部（含学前）
		// 小学预习情况 - 通过试卷绑定关系判断
		bindList := make([]string, 0, len(lessonIds))
		for _, lessonId := range lessonIds {
			bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, jxexamui.BindTypePreview)
			bindList = append(bindList, bindKey)
		}

		examRelationList, err := examcore.NewClient().GetRelation(ctx, bindList)
		if err != nil {
			zlog.Errorf(ctx, "GetPreviewOpenInfo GetRelation failed: %v", err)
			examRelationList = make(map[string]map[int64]examcore.BindInfo)
		}

		lessonList, err := s.GetLessonBaseInfo(ctx, lessonIds)
		if err != nil {
			zlog.Errorf(ctx, "GetPreviewOpenInfo GetLessonBaseInfo failed: %v", err)
			lessonList = make(map[int64]*dal.LessonInfo)
		}

		for _, lessonId := range lessonIds {
			bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, jxexamui.BindTypePreview)
			examRelationDetail := examRelationList[bindKey]

			hasPreview := 0
			for examId := range examRelationDetail {
				if examId > 0 {
					hasPreview = 1
					break
				}
			}

			isOpenPreview := 0
			if hasPreview == 1 {
				// 获取章节开始时间，判断是否在开课7天内
				if lessonInfo, ok := lessonList[lessonId]; ok {
					startTime := lessonInfo.StartTime
					// 与 PHP 对齐：使用开课“当天00:00”减去7天作为开启阈值
					loc := time.FixedZone("CST", 8*3600)
					t := time.Unix(int64(startTime), 0).In(loc)
					startDay := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, loc)
					sevenDaysBeforeStart := startDay.AddDate(0, 0, -7).Unix()
					if now > sevenDaysBeforeStart {
						isOpenPreview = 1
					}
				}
			}

			result[lessonId] = PreviewOpenInfo{
				HasPreview:    hasPreview,
				IsOpenPreview: isOpenPreview,
			}
		}

	case define.GradeStageJunior, define.GradeStageSenior: // 初高中
		// 初高中预习情况 - 通过frontcourse接口获取
		apiData, err := frontcourse.NewClient().GetHighGradePreviewInfo(ctx, lessonIds)
		if err != nil {
			zlog.Errorf(ctx, "GetPreviewOpenInfo GetHighGradePreviewInfo failed: %v", err)
			apiData = make(map[int64]frontcourse.HighGradePreviewInfo)
		}

		for _, lessonId := range lessonIds {
			hasPreview := 0
			isOpenPreview := 0

			if info, ok := apiData[lessonId]; ok {
				if info.Status > 0 {
					hasPreview = 1
				}
				isOpenPreview = info.IsOpen
			}

			result[lessonId] = PreviewOpenInfo{
				HasPreview:    hasPreview,
				IsOpenPreview: isOpenPreview,
			}
		}

	default:
	}

	return result, nil
}

// GetHomeworkOpenInfo 获取作业开启信息
// 对应PHP中的initHomeworkIsOpenInfos方法
func (s *Singleton) GetHomeworkOpenInfo(ctx *gin.Context, courseId int64) (map[int64]jxexamui.HomeworkOpenInfo, error) {
	result := make(map[int64]jxexamui.HomeworkOpenInfo)

	if courseId == 0 {
		return result, nil
	}

	courseInfo, err := s.GetCourseInfo(ctx, courseId)
	if err != nil {
		return nil, err
	}

	courseIdStr := strconv.FormatInt(courseId, 10)

	// 构建课程章节列表
	courseLessonList := make([]map[string]map[string]jxexamui.LessonDetail, 0)
	courseLesson := make(map[string]map[string]jxexamui.LessonDetail)
	courseLesson[courseIdStr] = make(map[string]jxexamui.LessonDetail)
	for lessonIdStr, lessonInfo := range courseInfo.LessonList {
		stopTime := lessonInfo.StopTime
		courseLesson[courseIdStr][lessonIdStr] = jxexamui.LessonDetail{
			StopTime: stopTime,
		}
	}
	courseLessonList = append(courseLessonList, courseLesson)

	// 调用jxexamui接口获取作业开启信息
	apiData, err := jxexamui.NewClient().GetHomeworkOpenTime(ctx, jxexamui.GetHomeworkOpenTimeReq{
		CourseIds: courseLessonList,
	})
	if err != nil {
		return nil, err
	}

	return apiData, nil
}

// GetHomeworkBindExams 获取章节作业绑定情况
// 对应PHP中的AssistantDesk_ExamBind::lessonBindExams方法
func (s *Singleton) GetHomeworkBindExams(ctx *gin.Context, lessonIds []int64, bindType int) (map[int64]bool, error) {
	result := make(map[int64]bool)

	if len(lessonIds) == 0 {
		return result, nil
	}

	// 构建绑定字符串列表
	bindList := make([]string, 0, len(lessonIds))
	for _, lessonId := range lessonIds {
		bindStr := fmt.Sprintf("lesson_%d:%d", lessonId, bindType)
		bindList = append(bindList, bindStr)
	}

	// 获取章节试卷绑定关系
	examRelationList, err := examcore.NewClient().GetRelation(ctx, bindList)
	if err != nil {
		return nil, err
	}

	// 检查每个章节是否有绑定
	for _, lessonId := range lessonIds {
		bindStr := fmt.Sprintf("lesson_%d:%d", lessonId, bindType)
		examRelationDetail := examRelationList[bindStr]
		result[lessonId] = len(examRelationDetail) > 0
	}

	return result, nil
}

// GetLessonNeedAuditMap 获取需要审核的课程映射
// 对应PHP中的lessonNeedAuditMap检查
func (s *Singleton) GetLessonNeedAuditMap(ctx *gin.Context, courseId, studentUid int64) (map[int64]bool, error) {
	result := make(map[int64]bool)

	if courseId <= 0 || studentUid <= 0 {
		return result, nil
	}

	// 调用 PcAssistant API 获取待审核章节列表
	client := pcassistant.NewClient()
	auditLessons, err := client.GetStudentHomeworkLessonNeedAudit(ctx, courseId, studentUid)
	if err != nil {
		zlog.Errorf(ctx, "GetLessonNeedAuditMap failed to get audit lessons: %v", err)
		return result, nil
	}

	// 构建章节ID映射
	for _, lesson := range auditLessons {
		result[lesson.LessonId] = true
	}

	return result, nil
}

// GetMonthlyExamReportLessons 获取有月考报告配置的课程章节
// 对应PHP中的Service_Data_MonthlyExamReport::getMonthlyExamReportLesson方法
func (s *Singleton) GetMonthlyExamReportLessons(ctx *gin.Context, courseId int64) (map[int64]bool, error) {
	result := make(map[int64]bool)

	if courseId <= 0 {
		return result, nil
	}

	courseInfo, err := s.GetCourseInfo(ctx, courseId)
	if err != nil {
		return nil, err
	}

	var validLessons []dal.LessonInfo
	lessonIds := make([]int64, 0, len(courseInfo.LessonList))
	for _, lesson := range courseInfo.LessonList {
		lessonIds = append(lessonIds, cast.ToInt64(lesson.LessonId))
		// 只处理未删除的主体章节
		if lesson.Status != define.LessonStatusDeleted && lesson.LessonType == define.LessonTypeMain {
			validLessons = append(validLessons, lesson)
		}
	}

	if len(validLessons) == 0 {
		zlog.Infof(ctx, "GetMonthlyExamReportLessons no valid main lessons found for course: %d", courseId)
		return result, nil
	}

	// 按开始时间排序
	sort.Slice(validLessons, func(i, j int) bool {
		return validLessons[i].StartTime < validLessons[j].StartTime
	})

	// 2. 检查堂堂测绑定情况
	examRelationMap, err := s.GetHomeworkBindExams(ctx, lessonIds, consts.BindTypeTestInClass)
	if err != nil {
		zlog.Errorf(ctx, "GetMonthlyExamReportLessons GetHomeworkBindExams failed: %v", err)
		examRelationMap = make(map[int64]bool)
	}

	// 过滤出绑定了堂堂测的章节
	var bindedLessonIds []int64
	for _, lessonId := range lessonIds {
		if examRelationMap[lessonId] {
			bindedLessonIds = append(bindedLessonIds, lessonId)
		}
	}

	if len(bindedLessonIds) == 0 {
		zlog.Infof(ctx, "GetMonthlyExamReportLessons no lessons with test-in-class binding found")
		return result, nil
	}

	// 3. 检查课程类型：浣熊课程或小学/学前的语数英课程
	isValidCourse := false

	// 如果不是浣熊课程，检查是否为小学/学前的语数英课程
	if !courseInfo.CheckIsHx(false) {
		subjectId := courseInfo.MainSubjectId
		departmentId := consts.GetDepartmentIdByGradeId(courseInfo.MainGradeId)
		if (departmentId == define.GradeStagePrimary || departmentId == define.GradeStagePreschool) &&
			(subjectId == consts.SubjectChinese || subjectId == consts.SubjectMath || subjectId == consts.SubjectEnglish) {
			isValidCourse = true
		}
	} else {
		isValidCourse = true
	}

	if !isValidCourse {
		zlog.Infof(ctx, "GetMonthlyExamReportLessons course %d is not valid for monthly exam report", courseId)
		return result, nil
	}

	// 4. 查询数据库配置
	result, err = models.AssistantMonthExamConfDao.GetMonthlyExamReportLessons(ctx, courseId, bindedLessonIds,
		courseInfo.MainSubjectId, courseInfo.Year, courseInfo.Season, courseInfo.ClassType, courseInfo.BookVer)
	if err != nil {
		return nil, err
	}

	return result, nil
}
